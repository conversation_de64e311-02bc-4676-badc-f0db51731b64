package sip

import (
	"context"
	"fmt"
	"log/slog"
	"strings"

	"gb-gateway/internal/config"
	gb28181handler "gb-gateway/internal/gb28181"
	"gb-gateway/internal/state"

	"github.com/go-av/gosip/pkg/dialog"
	"github.com/go-av/gosip/pkg/gb28181"
	"github.com/go-av/gosip/pkg/message"
	"github.com/go-av/gosip/pkg/server"
)

// Server SIP服务器
type Server struct {
	config       *config.Config
	stateManager state.Manager
	gb28181      *gb28181.GB28181
	handler      *gb28181handler.Handler
	ctx          context.Context
	cancel       context.CancelFunc
}

// NewServer 创建新的SIP服务器
func NewServer(cfg *config.Config, stateManager state.Manager) *Server {
	ctx, cancel := context.WithCancel(context.Background())

	// 创建GB28181处理器
	handler := gb28181handler.NewHandler(cfg, stateManager)

	server := &Server{
		config:       cfg,
		stateManager: stateManager,
		handler:      handler,
		ctx:          ctx,
		cancel:       cancel,
	}

	return server
}

// Start 启动SIP服务器
func (s *Server) Start() error {
	// 创建SIP服务器
	sipServer := server.NewServer(false, s) // 不需要认证

	// 创建GB28181实例
	s.gb28181 = gb28181.NewGB28181(sipServer, s.handler)

	// 在goroutine中启动SIP监听
	go func() {
		err := sipServer.SIPListen(s.ctx, s.config.Server.SIPIP, s.config.Server.SIPIP, uint16(s.config.Server.SIPPort), "udp")
		if err != nil {
			slog.Error("Failed to start SIP server", "error", err)
		}
	}()

	slog.Info("SIP server started", "listen_addr", fmt.Sprintf("%s:%d", s.config.Server.SIPIP, s.config.Server.SIPPort))

	return nil
}

// GetClient 实现server.Handler接口 - 获取客户端
func (s *Server) GetClient(deviceID string) (server.Client, error) {
	// 创建一个简单的客户端实现
	return &SimpleClient{
		deviceID: deviceID,
		auth:     true, // 简化处理，不需要认证
	}, nil
}

// Realm 实现server.Handler接口 - 返回SIP域
func (s *Server) Realm() string {
	return s.config.Server.SIPDomain
}

// ReceiveMessage 实现server.Handler接口 - 处理接收到的MESSAGE
func (s *Server) ReceiveMessage(ctx context.Context, client server.Client, body message.Body) (*server.Response, error) {
	slog.Debug("Received SIP MESSAGE", "device_id", client.User(), "content_type", body.ContentType())

	// 使用GB28181处理器处理消息
	response, err := s.gb28181.Handler(ctx, client, body.Data())
	if err != nil {
		slog.Error("Failed to handle GB28181 message", "error", err)
		return server.NewResponse(500, "Internal Server Error"), nil
	}

	if response == nil {
		return server.NewResponse(200, "OK"), nil
	}

	return response, nil
}

// SimpleClient 简单的客户端实现
type SimpleClient struct {
	deviceID string
	auth     bool
	protocol string
	address  string
}

func (c *SimpleClient) User() string {
	return c.deviceID
}

func (c *SimpleClient) Password() string {
	return "" // 简化处理
}

func (c *SimpleClient) IsAuth() bool {
	return c.auth
}

func (c *SimpleClient) SetAuth(auth bool) error {
	c.auth = auth
	return nil
}

func (c *SimpleClient) Transport() (string, string) {
	return c.protocol, c.address
}

func (c *SimpleClient) SetTransport(protocol, address string) {
	c.protocol = protocol
	c.address = address
}

func (c *SimpleClient) Logout() error {
	// 简化处理
	return nil
}

// Stop 停止SIP服务器
func (s *Server) Stop() {
	if s.cancel != nil {
		s.cancel()
	}
	slog.Info("SIP server stopped")
}

// SendInvite 发送INVITE请求
func (s *Server) SendInvite(gbID, receiveIP string, receivePort int, ssrc string) error {
	// 构造SDP内容
	sdpBody := fmt.Sprintf(`v=0
o=- 0 0 IN IP4 %s
s=Play
c=IN IP4 %s
t=0 0
m=video %d RTP/AVP 96
a=rtpmap:96 PS/90000
a=sendonly
a=ssrc:%s`, receiveIP, receiveIP, receivePort, ssrc)

	// 获取设备对应的平台信息
	platforms, err := s.stateManager.ListPlatforms()
	if err != nil || len(platforms) == 0 {
		return fmt.Errorf("no registered platform found")
	}

	platform := platforms[0]

	// 解析平台URI获取目标地址
	platformURI := platform.SIPURI
	platformURI = strings.TrimPrefix(platformURI, "sip:")

	// 分离用户部分和主机部分
	parts := strings.Split(platformURI, "@")
	if len(parts) != 2 {
		return fmt.Errorf("invalid platform SIP URI format: %s", platform.SIPURI)
	}

	platformHost := parts[1]

	// 获取客户端
	client, err := s.GetClient(gbID)
	if err != nil {
		return fmt.Errorf("failed to get client: %w", err)
	}

	// 设置客户端传输信息
	client.SetTransport("udp", platformHost)

	// 使用GB28181的Invite方法
	dl, err := s.gb28181.Invite(s.ctx, client, gbID, sdpBody, nil)
	if err != nil {
		// TODO: 处理超时是否算成功
		// 如果是超时错误，认为发送成功（因为SIP消息已经发送）
		if strings.Contains(err.Error(), "请求超时") || strings.Contains(err.Error(), "timeout") {
			slog.Warn("INVITE sent but response timeout", "gb_id", gbID, "error", err)
		} else {
			return fmt.Errorf("failed to send INVITE: %w", err)
		}
	}

	// TODO: 处理超时
	if dl != nil {
		slog.Info("INVITE dialog created", "gb_id", gbID)
		for {
			select {
			case <-dl.Context().Done():
				return fmt.Errorf("dialog timeout")
			case state := <-dl.State():
				slog.Info("接收状态更新:", "state", state.State().String())
				if state.State() == dialog.Accepted {
					slog.Info("对方已接听:", "SDP", string(dl.SDP()))
					// time.Sleep(20 * time.Second)
					// dl.Bye()
				}
				if state.State() == dialog.Error {
					slog.Info("错误:", "Reason", state.Reason())
				}
			}
		}
	}

	slog.Info("INVITE request sent successfully",
		"gb_id", gbID,
		"receive_ip", receiveIP,
		"receive_port", receivePort,
		"ssrc", ssrc,
		"target", platformHost)

	slog.Debug("SDP content", "sdp_body", sdpBody)

	return nil
}

// SendPTZControl 发送PTZ控制命令
func (s *Server) SendPTZControl(gbID, command string, speed int) error {
	// 构造PTZ控制字节
	ptzBytes := s.getPTZControlBytes(command, speed)

	// 获取设备对应的平台信息
	platforms, err := s.stateManager.ListPlatforms()
	if err != nil || len(platforms) == 0 {
		return fmt.Errorf("no registered platform found")
	}

	platform := platforms[0]

	// 解析平台URI获取目标地址
	platformURI := platform.SIPURI
	platformURI = strings.TrimPrefix(platformURI, "sip:")

	// 分离用户部分和主机部分
	parts := strings.Split(platformURI, "@")
	if len(parts) != 2 {
		return fmt.Errorf("invalid platform SIP URI format: %s", platform.SIPURI)
	}

	platformHost := parts[1]

	// 获取客户端
	client, err := s.GetClient(gbID)
	if err != nil {
		return fmt.Errorf("failed to get client: %w", err)
	}

	// 设置客户端传输信息
	client.SetTransport("udp", platformHost)

	// 使用GB28181的PTZControl方法
	err = s.gb28181.PTZControl(client, gbID, ptzBytes)
	if err != nil {
		return fmt.Errorf("failed to send PTZ control: %w", err)
	}

	slog.Info("PTZ control command sent successfully",
		"gb_id", gbID,
		"command", command,
		"speed", speed,
		"ptz_bytes", ptzBytes,
		"target", platformHost)

	return nil
}

// getPTZControlBytes 根据命令和速度生成PTZ控制字节
func (s *Server) getPTZControlBytes(command string, speed int) string {
	// PTZ控制字节格式：A50F01[方向][水平速度][垂直速度][变倍速度][变焦速度]
	// 基础字节
	bytes := []byte{0xA5, 0x0F, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00}

	// 设置速度（限制在1-255范围内）
	if speed < 1 {
		speed = 1
	}
	if speed > 255 {
		speed = 255
	}

	switch command {
	case "up":
		bytes[3] = 0x08 // 上
		bytes[5] = byte(speed)
	case "down":
		bytes[3] = 0x04 // 下
		bytes[5] = byte(speed)
	case "left":
		bytes[3] = 0x02 // 左
		bytes[4] = byte(speed)
	case "right":
		bytes[3] = 0x01 // 右
		bytes[4] = byte(speed)
	case "zoom_in":
		bytes[3] = 0x10 // 放大
		bytes[6] = byte(speed)
	case "zoom_out":
		bytes[3] = 0x20 // 缩小
		bytes[6] = byte(speed)
	case "stop":
		// 停止命令，所有字节保持0
	}

	// 转换为十六进制字符串
	hexStr := fmt.Sprintf("%02X%02X%02X%02X%02X%02X%02X%02X",
		bytes[0], bytes[1], bytes[2], bytes[3], bytes[4], bytes[5], bytes[6], bytes[7])

	return hexStr
}
